import React, { useContext, useEffect, useState } from "react";
import TreeSDK from "Utils/TreeSDK";
import FindByCoachTab from "Components/UserLessons/FindByCoachTab";
import CustomRequestTab from "Components/UserLessons/CustomRequestTab";
import { GlobalContext } from "Context/Global";
import LoadingSpinner from "Components/LoadingSpinner";
import MkdSDK from "Utils/MkdSDK";
import FindByTimeTab from "Components/UserLessons/FindByTimeTab";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useClub } from "Context/Club";

let tdk = new TreeSDK();
let sdk = new MkdSDK();

export default function UserLessons() {
  const [searchParams] = useSearchParams();
  const coachId = searchParams.get("coach");
  const [activeTab, setActiveTab] = useState("coach");
  const [coaches, setCoaches] = useState([]);
  const [players, setPlayers] = useState([]);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(true);
  const [groups, setGroups] = useState([]);
  const [userProfile, setUserProfile] = useState(null);
  const navigate = useNavigate();
  const tabs = [
    { id: "coach", label: "Find by coach" },
    { id: "time", label: "Find by time" },
    { id: "custom", label: "Custom request" },
  ];

  const { club, sports } = useClub();
  const user_id = localStorage.getItem("user");

  const fetchSports = async () => {
    try {
      const userResponse = await tdk.getOne("user", user_id, {});

      setUserProfile(userResponse.model);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchCoaches = async () => {
    const coachesResponse = await tdk.getList("coach", {
      join: [`user|user_id`],
      filter: [`courtmatchup_coach.club_id,eq,${parseInt(club?.id)}`],
    });
    const playersResponse = await tdk.getList("user", {
      filter: [`role,cs,user`, `club_id,eq,${parseInt(club?.id)}`],
    });
    setCoaches(coachesResponse.list);
    setPlayers(playersResponse.list);
  };
  const fetchGroups = async () => {
    try {
      const groupsResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/groups",
        {},
        "GET"
      );
      setGroups(groupsResponse.groups);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    (async () => {
      if (club?.id) {
        setIsLoading(true);
        await fetchSports();
        await fetchCoaches();
        await fetchGroups();
        setIsLoading(false);
      }
    })();
  }, [club?.id]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "lessons",
      },
    });

    // If coach parameter is present in URL, ensure we're on the coach tab
    if (coachId) {
      setActiveTab("coach");
    }
  }, [coachId]);

  return (
    <>
      <div className="bg-white px-3 py-3 sm:px-4 sm:py-4">
        {isLoading && <LoadingSpinner />}
        <h1 className="mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl">
          Lessons
        </h1>

        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          {/* Tabs */}
          <div className="flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-0 sm:text-sm">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${
                  activeTab === tab.id
                    ? "bg-white-600 font-medium"
                    : "bg-gray-100 text-gray-600"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
          {activeTab === "custom" && (
            <div className="flex justify-end">
              <button
                onClick={() => navigate("/user/create-custom-request")}
                className="rounded-lg bg-primaryBlue px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600 sm:px-4 sm:py-2"
              >
                Create request
              </button>
            </div>
          )}
        </div>
      </div>
      <div className="mx-auto max-w-7xl">
        {/* Tabs Content */}
        {activeTab === "coach" && (
          <FindByCoachTab
            sports={sports}
            coaches={coaches}
            players={players}
            groups={groups}
            club={club}
            userProfile={userProfile}
          />
        )}
        {activeTab === "time" && (
          <FindByTimeTab
            sports={sports}
            coaches={coaches}
            players={players}
            groups={groups}
            club={club}
            userProfile={userProfile}
          />
        )}
        {activeTab === "custom" && (
          <CustomRequestTab
            sports={sports}
            coaches={coaches}
            players={players}
            groups={groups}
            club={club}
            userProfile={userProfile}
          />
        )}
      </div>
    </>
  );
}
